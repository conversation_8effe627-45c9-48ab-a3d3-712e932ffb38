package com.ruoyi.system.service;

import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.impl.SysPasswordCryptoInitServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 密码加密初始化服务测试类
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class SysPasswordCryptoInitServiceTest {

    @Autowired
    private SysPasswordCryptoInitServiceImpl sysPasswordCryptoInitService;

    @Autowired
    private SysPasswordCryptoService sysPasswordCryptoService;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 测试用户ID为1的密码加密和解密
     */
    @Test
    public void testPasswordCryptoForUser1() {
        log.info("开始测试用户ID为1的密码加密和解密...");
        
        try {
            // 1. 查询用户ID为1的用户信息
            SysUser user = sysUserMapper.selectUserById(1L);
            if (user == null) {
                log.error("用户ID为1的用户不存在，测试终止");
                return;
            }
            
            log.info("找到用户 - ID: {}, 用户名: {}, 原始密码长度: {}", 
                user.getUserId(), user.getUserName(), 
                user.getPassword() != null ? user.getPassword().length() : 0);
            
            // 记录原始密码（用于后续验证）
            String originalPassword = user.getPassword();
            log.info("原始密码: {}", originalPassword);
            
            // 2. 检查密码格式
            boolean isBCryptFormat = sysPasswordCryptoService.isBCryptPassword(originalPassword);
            boolean isAlreadyEncrypted = sysPasswordCryptoService.isAlreadyEncrypted(originalPassword);
            
            log.info("密码格式检查 - 是BCrypt格式: {}, 已加密: {}", isBCryptFormat, isAlreadyEncrypted);
            
            // 3. 测试单独的加密功能
            log.info("=== 测试密码加密功能 ===");
            String encryptedPassword = sysPasswordCryptoService.encryptPassword(originalPassword);
            log.info("加密后密码: {}", encryptedPassword);
            log.info("加密后密码长度: {}", encryptedPassword != null ? encryptedPassword.length() : 0);
            
            // 4. 测试单独的解密功能
            log.info("=== 测试密码解密功能 ===");
            String decryptedPassword = sysPasswordCryptoService.decryptPassword(encryptedPassword);
            log.info("解密后密码: {}", decryptedPassword);
            log.info("解密后密码长度: {}", decryptedPassword != null ? decryptedPassword.length() : 0);
            
            // 5. 验证加密解密的一致性
            boolean isConsistent = originalPassword.equals(decryptedPassword);
            log.info("加密解密一致性验证: {}", isConsistent ? "通过" : "失败");
            
            if (!isConsistent) {
                log.error("加密解密不一致！原始: [{}], 解密后: [{}]", originalPassword, decryptedPassword);
            }
            
            // 6. 测试初始化方法（如果密码是BCrypt格式）
            if (isBCryptFormat) {
                log.info("=== 测试密码初始化功能 ===");
                
                // 执行初始化
                SysPasswordCryptoInitServiceImpl.PasswordCryptoInitResult result = 
                    sysPasswordCryptoInitService.initAllPasswordCrypto();
                
                log.info("初始化结果 - 总数: {}, 成功: {}, 失败: {}, 跳过: {}", 
                    result.totalCount, result.successCount, result.failCount, result.skippedCount);
                
                if (result.errorMessage != null) {
                    log.error("初始化错误: {}", result.errorMessage);
                }
                
                // 重新查询用户，检查密码是否已被加密
                SysUser updatedUser = sysUserMapper.selectUserById(1L);
                if (updatedUser != null) {
                    log.info("初始化后用户密码: {}", updatedUser.getPassword());
                    log.info("初始化后密码长度: {}", updatedUser.getPassword() != null ? updatedUser.getPassword().length() : 0);
                    
                    boolean isNowEncrypted = !sysPasswordCryptoService.isBCryptPassword(updatedUser.getPassword());
                    log.info("密码是否已加密: {}", isNowEncrypted);
                    
                    // 测试解密初始化后的密码
                    if (isNowEncrypted) {
                        String finalDecrypted = sysPasswordCryptoService.decryptPassword(updatedUser.getPassword());
                        log.info("初始化后解密密码: {}", finalDecrypted);
                        
                        boolean finalConsistent = originalPassword.equals(finalDecrypted);
                        log.info("初始化后解密一致性: {}", finalConsistent ? "通过" : "失败");
                    }
                }
            } else {
                log.info("密码已经是加密格式，跳过初始化测试");
            }
            
            log.info("测试完成！");
            
        } catch (Exception e) {
            log.error("测试过程中发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试批量初始化功能（仅处理用户ID为1的用户）
     */
    @Test
    public void testBatchPasswordCryptoForUser1() {
        log.info("开始测试批量密码加密初始化...");
        
        try {
            // 执行批量初始化，限制批次大小为1，最大批次为1
            SysPasswordCryptoInitServiceImpl.PasswordCryptoInitResult result = 
                sysPasswordCryptoInitService.initPasswordCryptoBatch(1, 1);
            
            log.info("批量初始化结果 - 总数: {}, 成功: {}, 失败: {}, 跳过: {}", 
                result.totalCount, result.successCount, result.failCount, result.skippedCount);
            
            if (result.errorMessage != null) {
                log.error("批量初始化错误: {}", result.errorMessage);
            } else {
                log.info("批量初始化测试完成！");
            }
            
        } catch (Exception e) {
            log.error("批量测试过程中发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试密码格式判断功能
     */
    @Test
    public void testPasswordFormatCheck() {
        log.info("开始测试密码格式判断功能...");
        
        // 测试BCrypt格式密码
        String bcryptPassword = "$2a$10$abcdefghijklmnopqrstuvwxyz1234567890";
        log.info("BCrypt密码测试: {}", bcryptPassword);
        log.info("是否为BCrypt格式: {}", sysPasswordCryptoService.isBCryptPassword(bcryptPassword));
        log.info("是否已加密: {}", sysPasswordCryptoService.isAlreadyEncrypted(bcryptPassword));
        
        // 测试非BCrypt格式密码（模拟SAC加密后的密码）
        String encryptedPassword = "1a2b3c4d5e6f7g8h9i0j";
        log.info("加密密码测试: {}", encryptedPassword);
        log.info("是否为BCrypt格式: {}", sysPasswordCryptoService.isBCryptPassword(encryptedPassword));
        log.info("是否已加密: {}", sysPasswordCryptoService.isAlreadyEncrypted(encryptedPassword));
        
        log.info("密码格式判断测试完成！");
    }
}
