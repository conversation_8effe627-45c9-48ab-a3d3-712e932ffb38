package com.ruoyi.auth.service;

import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysUser;

/**
 * 登录密码方法
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysPasswordService
{
    @Autowired
    private RedisService redisService;

    private int maxRetryCount = CacheConstants.passwordMaxRetryCount;

    private Long lockTime = CacheConstants.passwordLockTime;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired(required = false)
    private com.ruoyi.system.api.RemoteUserService remoteUserService;

    @Autowired(required = false)
    private com.ruoyi.common.security.context.PreTenantContext preTenantContext;

    /**
     * 登录账户密码错误次数缓存键名
     * 
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username)
    {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    public void validate(SysUser user, String password)
    {
        String username = user.getUserName();

        Integer retryCount = redisService.getCacheObject(getCacheKey(username));

        if (retryCount == null)
        {
            retryCount = 0;
        }

        if (retryCount >= Integer.valueOf(maxRetryCount).intValue())
        {
            String errMsg = String.format("密码输入错误%s次，帐户锁定%s分钟", maxRetryCount, lockTime);
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL,errMsg);
            throw new ServiceException(errMsg);
        }

        // 对管理员进行数据完整性验证
        if (isAdminUser(user)) {
            if (!verifyAdminDataIntegrity(user)) {
                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "管理员数据完整性验证失败");
                throw new ServiceException("管理员数据完整性验证失败，请联系管理员");
            }
        }

        if (!matches(user, password))
        {
            retryCount = retryCount + 1;
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, String.format("密码输入错误%s次", retryCount));
            redisService.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            throw new ServiceException("用户不存在/密码错误");
        }
        else
        {
            clearLoginRecordCache(username);
        }
    }

    public boolean matches(SysUser user, String rawPassword)
    {
        // 先解密数据库中的密码，然后进行比较
        String passwordToCompare = user.getPassword();

        // 检查密码是否使用了SAC加密（以{SAC}开头）
        if (passwordToCompare != null && passwordToCompare.startsWith("{SAC}")) {
            try {
                // 调用远程服务解密密码
                com.ruoyi.common.core.domain.R<String> decryptResult =
                    remoteUserService.decryptPassword(passwordToCompare, com.ruoyi.common.core.constant.SecurityConstants.INNER);

                if (decryptResult != null && com.ruoyi.common.core.domain.R.SUCCESS == decryptResult.getCode()
                    && decryptResult.getData() != null) {
                    passwordToCompare = decryptResult.getData();
                    log.debug("密码解密成功，用户名: {}", user.getUserName());
                } else {
                    log.warn("密码解密失败，用户名: {}, 响应码: {}", user.getUserName(),
                        decryptResult != null ? decryptResult.getCode() : "null");
                    // 解密失败时使用原密码进行比较
                }
            } catch (Exception e) {
                log.error("密码解密异常，用户名: {}, 错误: {}", user.getUserName(), e.getMessage());
                // 解密异常时使用原密码进行比较
            }
        }

        return SecurityUtils.matchesPassword(rawPassword, passwordToCompare);
    }

    public void clearLoginRecordCache(String loginName)
    {
        if (redisService.hasKey(getCacheKey(loginName)))
        {
            redisService.deleteObject(getCacheKey(loginName));
        }
    }

    /**
     * 判断是否为管理员用户
     *
     * @param user 用户信息
     * @return 是否为管理员
     */
    private boolean isAdminUser(SysUser user) {
        // 判断用户名是否为ecsmaster（超级管理员）
        boolean isAdmin = "ecsmaster".equals(user.getUserName());
        log.info("用户身份检查 - 用户名: {}, 用户ID: {}, 是否为管理员: {}",
            user.getUserName(), user.getUserId(), isAdmin);
        return isAdmin;
    }

    /**
     * 验证管理员数据完整性
     * 包括：用户表签名、用户角色关联表签名、角色菜单关联表签名
     *
     * @param user 用户信息
     * @return 验证结果
     */
    private boolean verifyAdminDataIntegrity(SysUser user) {
        log.info("开始管理员数据完整性验证 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

        try {
            if (remoteUserService == null) {
                log.warn("远程用户服务不可用，跳过管理员数据完整性验证 - 用户名: {}", user.getUserName());
                return true;
            }

            // 设置app_id上下文
            if (preTenantContext != null) {
                preTenantContext.setCurrentAppId("2");
                log.info("设置app_id上下文为2 - 用户名: {}", user.getUserName());
            }

            // 1. 验证用户表签名
            log.info("开始验证用户表签名 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
            if (!verifyUserTableSignature(user)) {
                log.error("用户表签名验证失败 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
                return false;
            }
            log.info("用户表签名验证通过 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

            // 2. 验证用户角色关联表签名
            log.info("开始验证用户角色关联表签名 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
            if (!verifyUserRoleSignature(user)) {
                log.error("用户角色关联表签名验证失败 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
                return false;
            }
            log.info("用户角色关联表签名验证通过 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

            // 3. 验证角色菜单关联表签名
            log.info("开始验证角色菜单关联表签名 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
            if (!verifyRoleMenuSignature(user)) {
                log.error("角色菜单关联表签名验证失败 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
                return false;
            }
            log.info("角色菜单关联表签名验证通过 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

            log.info("管理员数据完整性验证全部通过 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());
            return true;
        } catch (Exception e) {
            log.error("管理员数据完整性验证异常 - 用户名: {}, 用户ID: {}, 错误: {}",
                user.getUserName(), user.getUserId(), e.getMessage(), e);
            // 验证异常时，记录日志但不阻止登录
            return true;
        }
    }

    /**
     * 验证用户表签名
     */
    private boolean verifyUserTableSignature(SysUser user) {
        try {
            log.info("构建用户表签名验证请求 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

            com.ruoyi.system.api.dto.VerifySignatureReqDto reqDto = new com.ruoyi.system.api.dto.VerifySignatureReqDto();
            reqDto.setTableName("sys_user");
            reqDto.setTableField("user_name,password,phonenumber");
            reqDto.setRecordId(String.valueOf(user.getUserId()));

            // 构建待验证数据（直接拼接字段值）
            // 注意：如果密码是SAC加密的，需要先解密再用于验证
            StringBuilder sb = new StringBuilder();
            sb.append(user.getUserName() != null ? user.getUserName() : "");

            // 处理密码：如果是SAC加密的密码，需要先解密
            String passwordForVerify = user.getPassword();
            if (passwordForVerify != null && passwordForVerify.startsWith("{SAC}")) {
                try {
                    com.ruoyi.common.core.domain.R<String> decryptResult =
                        remoteUserService.decryptPassword(passwordForVerify, com.ruoyi.common.core.constant.SecurityConstants.INNER);

                    if (decryptResult != null && com.ruoyi.common.core.domain.R.SUCCESS == decryptResult.getCode()
                        && decryptResult.getData() != null) {
                        passwordForVerify = decryptResult.getData();
                        log.debug("签名验证时密码解密成功，用户名: {}", user.getUserName());
                    } else {
                        log.warn("签名验证时密码解密失败，用户名: {}, 响应码: {}", user.getUserName(),
                            decryptResult != null ? decryptResult.getCode() : "null");
                    }
                } catch (Exception e) {
                    log.error("签名验证时密码解密异常，用户名: {}, 错误: {}", user.getUserName(), e.getMessage());
                }
            }

            sb.append(passwordForVerify != null ? passwordForVerify : "");
            sb.append(user.getPhonenumber() != null ? user.getPhonenumber() : "");
            reqDto.setDataToVerify(sb.toString());

            log.info("用户表签名验证数据 - 用户名: {}, 记录ID: {}, 数据长度: {}",
                user.getUserName(), reqDto.getRecordId(), reqDto.getDataToVerify().length());

            com.ruoyi.common.core.domain.R<Boolean> result = remoteUserService.verifySignature(reqDto, com.ruoyi.common.core.constant.SecurityConstants.INNER);

            boolean isValid = result != null && com.ruoyi.common.core.domain.R.SUCCESS == result.getCode() && Boolean.TRUE.equals(result.getData());

            log.info("用户表签名验证结果 - 用户名: {}, 验证结果: {}, 响应码: {}, 响应数据: {}",
                user.getUserName(), isValid,
                result != null ? result.getCode() : "null",
                result != null ? result.getData() : "null");

            return isValid;
        } catch (Exception e) {
            log.error("用户表签名验证异常 - 用户名: {}, 错误: {}", user.getUserName(), e.getMessage(), e);
            return true;
        }
    }

    /**
     * 验证用户角色关联表签名
     */
    private boolean verifyUserRoleSignature(SysUser user) {
        try {
            log.info("开始获取用户角色列表 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

            // 获取用户的所有角色ID
            com.ruoyi.common.core.domain.R<java.util.List<Long>> roleResult = remoteUserService.selectRoleListByUserId(user.getUserId(), com.ruoyi.common.core.constant.SecurityConstants.INNER);
            if (roleResult == null || com.ruoyi.common.core.domain.R.SUCCESS != roleResult.getCode() || roleResult.getData() == null) {
                log.info("获取用户角色列表失败，跳过用户角色关联表签名验证 - 用户名: {}, 响应码: {}",
                    user.getUserName(), roleResult != null ? roleResult.getCode() : "null");
                return true; // 如果获取角色失败，跳过验证
            }

            java.util.List<Long> roleIds = roleResult.getData();
            log.info("获取到用户角色列表 - 用户名: {}, 角色数量: {}, 角色ID: {}",
                user.getUserName(), roleIds.size(), roleIds);

            if (roleIds.isEmpty()) {
                log.info("用户无角色关联，跳过用户角色关联表签名验证 - 用户名: {}", user.getUserName());
                return true; // 如果没有角色，跳过验证
            }

            com.ruoyi.system.api.dto.VerifySignatureReqDto reqDto = new com.ruoyi.system.api.dto.VerifySignatureReqDto();
            reqDto.setTableName("sys_user_role");
            reqDto.setTableField("user_id,role_ids,app_id");
            reqDto.setRecordId(String.valueOf(user.getUserId()));

            // 构建待验证数据（用户ID + 排序的角色ID + app_id）
            StringBuilder sb = new StringBuilder();
            sb.append(user.getUserId().toString());
            roleIds.stream().sorted().forEach(roleId -> sb.append(roleId.toString()));
            sb.append("2"); // app_id
            reqDto.setDataToVerify(sb.toString());

            log.info("用户角色关联表签名验证数据 - 用户名: {}, 记录ID: {}, 数据长度: {}",
                user.getUserName(), reqDto.getRecordId(), reqDto.getDataToVerify().length());

            com.ruoyi.common.core.domain.R<Boolean> result = remoteUserService.verifySignature(reqDto, com.ruoyi.common.core.constant.SecurityConstants.INNER);

            boolean isValid = result != null && com.ruoyi.common.core.domain.R.SUCCESS == result.getCode() && Boolean.TRUE.equals(result.getData());

            log.info("用户角色关联表签名验证结果 - 用户名: {}, 验证结果: {}, 响应码: {}, 响应数据: {}",
                user.getUserName(), isValid,
                result != null ? result.getCode() : "null",
                result != null ? result.getData() : "null");

            return isValid;
        } catch (Exception e) {
            log.error("用户角色关联表签名验证异常 - 用户名: {}, 错误: {}", user.getUserName(), e.getMessage(), e);
            return true;
        }
    }

    /**
     * 验证角色菜单关联表签名
     */
    private boolean verifyRoleMenuSignature(SysUser user) {
        try {
            log.info("开始获取用户角色列表用于菜单验证 - 用户名: {}, 用户ID: {}", user.getUserName(), user.getUserId());

            // 获取用户的所有角色ID
            com.ruoyi.common.core.domain.R<java.util.List<Long>> roleResult = remoteUserService.selectRoleListByUserId(user.getUserId(), com.ruoyi.common.core.constant.SecurityConstants.INNER);
            if (roleResult == null || com.ruoyi.common.core.domain.R.SUCCESS != roleResult.getCode() || roleResult.getData() == null) {
                log.info("获取用户角色列表失败，跳过角色菜单关联表签名验证 - 用户名: {}, 响应码: {}",
                    user.getUserName(), roleResult != null ? roleResult.getCode() : "null");
                return true;
            }

            java.util.List<Long> roleIds = roleResult.getData();
            log.info("获取到用户角色列表用于菜单验证 - 用户名: {}, 角色数量: {}, 角色ID: {}",
                user.getUserName(), roleIds.size(), roleIds);

            if (roleIds.isEmpty()) {
                log.info("用户无角色关联，跳过角色菜单关联表签名验证 - 用户名: {}", user.getUserName());
                return true;
            }

            // 验证每个角色的菜单关联签名
            for (Long roleId : roleIds) {
                log.info("开始验证角色菜单关联 - 用户名: {}, 角色ID: {}", user.getUserName(), roleId);

                // 获取角色的所有菜单ID
                com.ruoyi.common.core.domain.R<java.util.List<Long>> menuResult = remoteUserService.selectMenuListByRoleId(roleId, com.ruoyi.common.core.constant.SecurityConstants.INNER);
                if (menuResult == null || com.ruoyi.common.core.domain.R.SUCCESS != menuResult.getCode() || menuResult.getData() == null) {
                    log.info("获取角色菜单列表失败，跳过该角色 - 用户名: {}, 角色ID: {}, 响应码: {}",
                        user.getUserName(), roleId, menuResult != null ? menuResult.getCode() : "null");
                    continue;
                }

                java.util.List<Long> menuIds = menuResult.getData();
                log.info("获取到角色菜单列表 - 用户名: {}, 角色ID: {}, 菜单数量: {}, 菜单ID: {}",
                    user.getUserName(), roleId, menuIds.size(), menuIds);

                if (menuIds.isEmpty()) {
                    log.info("角色无菜单关联，跳过该角色 - 用户名: {}, 角色ID: {}", user.getUserName(), roleId);
                    continue;
                }

                com.ruoyi.system.api.dto.VerifySignatureReqDto reqDto = new com.ruoyi.system.api.dto.VerifySignatureReqDto();
                reqDto.setTableName("sys_role_menu");
                reqDto.setTableField("role_id,menu_ids");
                reqDto.setRecordId(String.valueOf(roleId));

                // 构建待验证数据（角色ID + 排序的菜单ID）
                StringBuilder sb = new StringBuilder();
                sb.append(roleId.toString());
                menuIds.stream().sorted().forEach(menuId -> sb.append(menuId.toString()));
                reqDto.setDataToVerify(sb.toString());

                log.info("角色菜单关联表签名验证数据 - 用户名: {}, 角色ID: {}, 记录ID: {}, 数据长度: {}",
                    user.getUserName(), roleId, reqDto.getRecordId(), reqDto.getDataToVerify().length());

                com.ruoyi.common.core.domain.R<Boolean> result = remoteUserService.verifySignature(reqDto, com.ruoyi.common.core.constant.SecurityConstants.INNER);

                boolean isValid = result != null && com.ruoyi.common.core.domain.R.SUCCESS == result.getCode() && Boolean.TRUE.equals(result.getData());

                log.info("角色菜单关联表签名验证结果 - 用户名: {}, 角色ID: {}, 验证结果: {}, 响应码: {}, 响应数据: {}",
                    user.getUserName(), roleId, isValid,
                    result != null ? result.getCode() : "null",
                    result != null ? result.getData() : "null");

                if (!isValid) {
                    log.error("角色菜单关联表签名验证失败 - 用户名: {}, 角色ID: {}", user.getUserName(), roleId);
                    return false; // 任何一个角色的菜单签名验证失败都返回false
                }
            }

            log.info("所有角色菜单关联表签名验证通过 - 用户名: {}, 验证角色数量: {}", user.getUserName(), roleIds.size());
            return true;
        } catch (Exception e) {
            log.error("角色菜单关联表签名验证异常 - 用户名: {}, 错误: {}", user.getUserName(), e.getMessage(), e);
            return true;
        }
    }
}
